import React, { useEffect, useState, useTransition } from 'react'
import { <PERSON>rowserRouter, Routes, Route, useLocation } from 'react-router-dom'
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Header from './Components/Header'
import Footer from './Components/Footer'
import LoadingScreen from './Components/LoadingScreen'
import Home from './Pages/Home'
import Properties from './Pages/Properties'
import Loans from './Pages/Loans'
import Interiors from './Pages/Interiors'
import Services from './Pages/Services'
import About from './Pages/About'
import Contact from './Pages/Contact'
import Career from './Pages/Career'
import Mortgage from './Components/MortgageLoan'
import EmiCalculatorPage from './Pages/EmiCalculatorPage'
import TermsAndCondition from './Pages/TermsAndCondition'
import NotFound from './Pages/NotFound'

// ScrollToTop component to handle scroll restoration
const ScrollToTop = () => {
  const { pathname } = useLocation();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
  
  return null;
};

// PageTransition component to handle smooth transitions
const PageTransition = ({ children }) => {
  const location = useLocation();
  const [isPending, startTransition] = useTransition();
  const [displayLocation, setDisplayLocation] = useState(location);
  const [transitionStage, setTransitionStage] = useState("fadeIn");

  useEffect(() => {
    if (location !== displayLocation) {
      setTransitionStage("fadeOut");
      
      // After the fadeOut animation completes, update the location
      const timeout = setTimeout(() => {
        startTransition(() => {
          setDisplayLocation(location);
          setTransitionStage("fadeIn");
        });
      }, 300); // Match this with the CSS transition duration
      
      return () => clearTimeout(timeout);
    }
  }, [location, displayLocation, startTransition]);

  return (
    <>
      <LoadingScreen isVisible={isPending} />
      
      <div className={`transition-opacity duration-300 ${transitionStage === "fadeOut" ? "opacity-0" : "opacity-100"}`}>
        {children}
      </div>
    </>
  );
};

const App = () => {
  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <ScrollToTop />
      <Header />
      <PageTransition>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/properties" element={<Properties />} />
          <Route path="/loans" element={<Loans />} />
          <Route path="/interiors" element={<Interiors />} />
          <Route path="/services" element={<Services />} />
          <Route path="/about" element={<About />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/career" element={<Career />} />
          <Route path="/mortgage" element={<Mortgage />} />
          <Route path="/emi-calculator" element={<EmiCalculatorPage />} />
          <Route path="/terms-and-condition" element={<TermsAndCondition />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </PageTransition>
      <Footer />
      
      {/* Floating WhatsApp Button */}
      <a 
        href="https://wa.me/919998860713" 
        target="_blank" 
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg hover:bg-green-600 transition-colors z-50"
        aria-label="Chat on WhatsApp"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="white" className="bi bi-whatsapp" viewBox="0 0 16 16">
          <path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592z"/>
        </svg>
      </a>
      
      {/* Toast Container */}
      <ToastContainer 
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
    </BrowserRouter>
  );
};

export default App;
