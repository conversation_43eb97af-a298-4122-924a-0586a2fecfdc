import { toast } from 'react-toastify';

export const submitForm = async (formData) => {
  try {
    const response = await fetch('https://formspree.io/f/YOUR_FORMSPREE_ID', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...formData,
        _replyto: formData.email || formData.contact,
        _subject: `New submission from ${formData.form_name}`
      })
    });
    
    if (response.ok) {
      toast.success('Form submitted successfully!');
      return { success: true };
    } else {
      toast.error('Form submission failed. Please try again.');
      throw new Error('Form submission failed');
    }
  } catch (error) {
    console.error('Error submitting form:', error);
    toast.error('An error occurred. Please try again later.');
    throw error;
  }
};
