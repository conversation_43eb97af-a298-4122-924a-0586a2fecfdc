import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("56QIUeVZvQvuTnM9g"); // Replace with your actual EmailJS User ID

export const sendFormData = (templateId, formData) => {
  const primaryEmail = "<EMAIL>";
  const secondaryEmail = "<EMAIL>";

  const templateParams = {
    to_email: primaryEmail,
    cc_email: secondaryEmail, // Add CC email
    from_email: primaryEmail,
    reply_to: primaryEmail,
    // Include all user-entered data
    ...formData,
    // Format user data for email body
    user_data: formatUserData(formData),
    // Add timestamp
    submission_date: new Date().toLocaleString()
  };

  // Send to primary email
  const primaryEmailPromise = emailjs.send(
    "service_yo96gli", // Your EmailJS service ID
    templateId,
    templateParams
  );

  // Send to secondary email (<EMAIL>)
  const secondaryTemplateParams = {
    ...templateParams,
    to_email: secondaryEmail,
    from_email: primaryEmail
  };

  const secondaryEmailPromise = emailjs.send(
    "service_yo96gli", // Your EmailJS service ID
    templateId,
    secondaryTemplateParams
  );

  // Send to both emails
  return Promise.all([primaryEmailPromise, secondaryEmailPromise])
    .then(responses => {
      toast.success('Form submitted successfully to both email addresses!');
      return responses;
    })
    .catch(error => {
      toast.error('Form submission failed. Please try again.');
      throw error;
    });
};

// Helper function to format user data for email display
const formatUserData = (formData) => {
  let formattedData = '';

  // Exclude internal fields from display
  const excludeFields = ['form_name', 'to_email', 'cc_email', 'from_email', 'reply_to', 'user_data', 'submission_date'];

  Object.entries(formData).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value) {
      // Convert camelCase to readable format
      const readableKey = key.replace(/([A-Z])/g, ' $1')
                            .replace(/^./, str => str.toUpperCase())
                            .replace(/_/g, ' ');
      formattedData += `${readableKey}: ${value}\n`;
    }
  });

  return formattedData;
};
