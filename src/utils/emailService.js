import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("56QIUeVZvQvuTnM9g");

export const sendFormData = (formData) => {
  const primaryEmail = "<EMAIL>";
  const secondaryEmail = "<EMAIL>";

  // Create comprehensive template parameters that work with most EmailJS templates
  const templateParams = {
    // Basic EmailJS template variables (most common)
    to_name: "OneStopSolution Team",
    to_email: primaryEmail,
    from_name: formData.name || "Website User",
    from_email: formData.email || "<EMAIL>",
    reply_to: formData.email || primaryEmail,

    // Message content
    subject: `${formData.form_name || 'Form Submission'} - ${new Date().toLocaleDateString()}`,
    message: createEmailMessage(formData),

    // Individual fields (common template variables)
    name: formData.name || '',
    email: formData.email || '',
    phone: formData.phone || formData.contact || '',
    contact: formData.contact || formData.phone || '',

    // Form-specific fields
    form_type: formData.form_name || 'Form Submission',
    loan_type: formData.loan_type || formData.loanType || '',
    amount: formData.amount || '',
    income: formData.income || '',
    city: formData.city || '',
    property_type: formData.property_type || formData.propertyType || '',
    project_type: formData.project_type || formData.projectType || '',
    budget: formData.budget || '',
    user_message: formData.message || '',

    // Additional common variables
    company: "OneStopSolution",
    website: "onestopsolution.com",
    timestamp: new Date().toLocaleString()
  };

  console.log('📧 Sending email with template_e7mqh3d:', templateParams);

  // Function to send email
  const sendEmail = async (targetEmail) => {
    const params = { ...templateParams, to_email: targetEmail };

    try {
      const result = await emailjs.send("service_yo96gli", "template_e7mqh3d", params);
      console.log(`✅ Email sent successfully to ${targetEmail}:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Failed to send email to ${targetEmail}:`, error);
      throw error;
    }
  };

  // Send to both emails
  return Promise.allSettled([
    sendEmail(primaryEmail),
    sendEmail(secondaryEmail)
  ])
    .then(results => {
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');

      console.log('📊 Final Results:', {
        successful: successful.length,
        failed: failed.length,
        errors: failed.map(f => f.reason?.text || f.reason?.message)
      });

      if (successful.length > 0) {
        if (successful.length === 2) {
          toast.success('✅ Form submitted successfully to both email addresses!');
        } else {
          toast.success('✅ Form submitted successfully to one email address!');
        }
        return successful.map(result => result.value);
      } else {
        // All failed - provide helpful error message
        const errorText = failed[0]?.reason?.text || 'Unknown error';
        console.error('🚨 All emails failed. Error:', errorText);

        if (errorText.includes('template')) {
          toast.error('❌ EmailJS template issue. Please check your template configuration.');
        } else if (errorText.includes('service')) {
          toast.error('❌ EmailJS service issue. Please check your service configuration.');
        } else {
          toast.error('❌ Email sending failed. Please try again or contact support.');
        }

        throw new Error(`All email attempts failed: ${errorText}`);
      }
    });
};

// Helper function to create formatted email message
const createEmailMessage = (formData) => {
  let message = `New ${formData.form_name || 'Form Submission'}\n\n`;
  message += `Submitted on: ${new Date().toLocaleString()}\n\n`;
  message += "Details:\n";
  message += "=".repeat(40) + "\n";

  // Add all form fields
  Object.entries(formData).forEach(([key, value]) => {
    if (value && key !== 'form_name') {
      const label = key.replace(/([A-Z])/g, ' $1')
                      .replace(/^./, str => str.toUpperCase())
                      .replace(/_/g, ' ');
      message += `${label}: ${value}\n`;
    }
  });

  message += "=".repeat(40) + "\n";
  message += "Sent from OneStopSolution website";

  return message;
};


