import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("56QIUeVZvQvuTnM9g");

export const sendFormData = (formData) => {
  const primaryEmail = "<EMAIL>";
  const secondaryEmail = "<EMAIL>";

  // Using your existing template ID: template_e7mqh3d

  const templateParams = {
    to_email: primaryEmail,
    from_name: formData.name || "Website User",
    from_email: formData.email || primaryEmail,
    reply_to: formData.email || primaryEmail,
    subject: `${formData.form_name || 'Form Submission'} - ${new Date().toLocaleDateString()}`,
    message: formatUserData(formData),
    // Standard fields
    name: formData.name || '',
    email: formData.email || '',
    phone: formData.phone || formData.contact || '',
    user_message: formData.message || formatUserData(formData),
    form_type: formData.form_name || 'Form Submission'
  };

  console.log('📧 Attempting to send email with params:', templateParams);

  // Try to send email with error handling
  const sendEmail = (targetEmail, templateParams) => {
    const params = { ...templateParams, to_email: targetEmail };
    return emailjs.send("service_yo96gli", "template_e7mqh3d", params);
  };

  // Send to both emails
  const primaryEmailPromise = sendEmail(primaryEmail, templateParams);
  const secondaryEmailPromise = sendEmail(secondaryEmail, templateParams);

  return Promise.allSettled([primaryEmailPromise, secondaryEmailPromise])
    .then(results => {
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');

      console.log('📊 Email results:', { successful: successful.length, failed: failed.length });

      if (failed.length > 0) {
        console.error('❌ Failed email attempts:', failed.map(f => f.reason));

        // Show specific error message
        const errorMessage = failed[0].reason?.text || failed[0].reason?.message || 'Unknown error';
        if (errorMessage.includes('template ID not found')) {
          toast.error('❌ EmailJS Template Missing! Please check template "template_e7mqh3d" in your EmailJS dashboard.');
          console.error('🔧 SOLUTION: Make sure template "template_e7mqh3d" exists in your EmailJS dashboard');
        } else {
          toast.error(`❌ Email failed: ${errorMessage}`);
        }
      }

      if (successful.length > 0) {
        if (successful.length === 2) {
          toast.success('✅ Form submitted successfully to both email addresses!');
        } else {
          toast.success('✅ Form submitted successfully to one email address!');
        }
        return successful.map(result => result.value);
      } else {
        throw new Error('All email attempts failed');
      }
    })
    .catch(error => {
      console.error('💥 EmailJS Error Details:', error);
      throw error;
    });
};

// Helper function to format user data for email display
const formatUserData = (formData) => {
  let formattedData = '';

  // Exclude internal fields from display
  const excludeFields = ['form_name', 'to_email', 'cc_email', 'from_email', 'reply_to', 'user_data', 'submission_date'];

  Object.entries(formData).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value) {
      // Convert camelCase to readable format
      const readableKey = key.replace(/([A-Z])/g, ' $1')
                            .replace(/^./, str => str.toUpperCase())
                            .replace(/_/g, ' ');
      formattedData += `${readableKey}: ${value}\n`;
    }
  });

  return formattedData;
};
