import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("56QIUeVZvQvuTnM9g"); // Replace with your actual EmailJS User ID

export const sendFormData = (templateId, formData) => {
  const templateParams = {
    to_email: "<EMAIL>", // Replace with your actual recipient email address
    ...formData
  };

  return emailjs.send(
    "service_yo96gli", // Replace with your EmailJS service ID
    templateId,
    templateParams
  )
  .then(response => {
    toast.success('Form submitted successfully!');
    return response;
  })
  .catch(error => {
    toast.error('Form submission failed. Please try again.');
    throw error;
  });
};
