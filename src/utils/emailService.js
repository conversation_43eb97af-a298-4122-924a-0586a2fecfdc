import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("56QIUeVZvQvuTnM9g"); // Replace with your actual EmailJS User ID

export const sendFormData = (formData) => {
  const primaryEmail = "<EMAIL>";
  const secondaryEmail = "<EMAIL>";

  // Use a single template for all forms to avoid template not found errors
  const useTemplateId = "template_contact"; // Make sure this template exists in your EmailJS account

  const templateParams = {
    to_email: primaryEmail,
    from_name: formData.name || "Website User",
    from_email: formData.email || primaryEmail,
    reply_to: formData.email || primaryEmail,
    subject: `${formData.form_name || 'Form Submission'} - ${new Date().toLocaleDateString()}`,
    message: formatUserData(formData),
    // Standard fields that should work with most templates
    name: formData.name || '',
    email: formData.email || '',
    phone: formData.phone || formData.contact || '',
    user_message: formData.message || formatUserData(formData),
    form_type: formData.form_name || 'Form Submission'
  };

  console.log('Sending email with params:', templateParams); // Debug log

  // Send to primary email
  const sendToPrimary = emailjs.send("service_yo96gli", useTemplateId, templateParams);

  // Send to secondary email
  const secondaryParams = { ...templateParams, to_email: secondaryEmail };
  const sendToSecondary = emailjs.send("service_yo96gli", useTemplateId, secondaryParams);

  // Send to both emails
  return Promise.allSettled([sendToPrimary, sendToSecondary])
    .then(results => {
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');

      console.log('Email results:', { successful: successful.length, failed: failed.length });

      if (failed.length > 0) {
        console.error('Failed email attempts:', failed.map(f => f.reason));
      }

      if (successful.length > 0) {
        if (successful.length === 2) {
          toast.success('Form submitted successfully to both email addresses!');
        } else {
          toast.success('Form submitted successfully to one email address!');
        }
        return successful.map(result => result.value);
      } else {
        throw new Error('All email attempts failed');
      }
    })
    .catch(error => {
      console.error('EmailJS Error Details:', error);
      toast.error('Form submission failed. Please check your EmailJS configuration.');
      throw error;
    });
};

// Helper function to format user data for email display
const formatUserData = (formData) => {
  let formattedData = '';

  // Exclude internal fields from display
  const excludeFields = ['form_name', 'to_email', 'cc_email', 'from_email', 'reply_to', 'user_data', 'submission_date'];

  Object.entries(formData).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value) {
      // Convert camelCase to readable format
      const readableKey = key.replace(/([A-Z])/g, ' $1')
                            .replace(/^./, str => str.toUpperCase())
                            .replace(/_/g, ' ');
      formattedData += `${readableKey}: ${value}\n`;
    }
  });

  return formattedData;
};
