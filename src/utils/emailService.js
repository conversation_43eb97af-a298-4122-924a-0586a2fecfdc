import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

// Initialize EmailJS with your user ID
emailjs.init("YOUR_USER_ID"); // Replace with your actual EmailJS User ID

export const sendFormData = (templateId, formData) => {
  const targetEmail = "<EMAIL>";

  // Prepare template parameters with both to and from email as the same address
  const templateParams = {
    to_email: targetEmail,
    from_email: targetEmail,
    reply_to: targetEmail,
    // Include all user-entered data
    ...formData,
    // Format user data for email body
    user_data: formatUserData(formData),
    // Add timestamp
    submission_date: new Date().toLocaleString()
  };

  return emailjs.send(
    "YOUR_SERVICE_ID", // Replace with your EmailJS service ID
    templateId,
    templateParams
  )
  .then(response => {
    toast.success('Form submitted successfully!');
    return response;
  })
  .catch(error => {
    toast.error('Form submission failed. Please try again.');
    throw error;
  });
};

// Helper function to format user data for email display
const formatUserData = (formData) => {
  let formattedData = '';

  // Exclude internal fields from display
  const excludeFields = ['form_name', 'to_email', 'from_email', 'reply_to', 'user_data', 'submission_date'];

  Object.entries(formData).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value) {
      // Convert camelCase to readable format
      const readableKey = key.replace(/([A-Z])/g, ' $1')
                            .replace(/^./, str => str.toUpperCase())
                            .replace(/_/g, ' ');
      formattedData += `${readableKey}: ${value}\n`;
    }
  });

  return formattedData;
};
