import React from 'react';

const MortgageLoan = () => {
  const documentRequirements = [
    {
      category: "Identity & Address Proof",
      documents: [
        "PAN Card",
        "Aadhaar Card",
        "Passport",
        "Voter's ID",
        "Driving License",
        "Utility Bills (not older than 3 months)"
      ]
    },
    {
      category: "Income Documents (Salaried)",
      documents: [
        "Last 3 months salary slips",
        "Form 16 for the last 2 years",
        "Last 6 months bank statements showing salary credits",
        "Employment certificate",
        "Income Tax Returns for the last 2 years"
      ]
    },
    {
      category: "Income Documents (Self-Employed)",
      documents: [
        "Income Tax Returns for the last 3 years",
        "Business registration documents",
        "GST returns (if applicable)",
        "Last 12 months bank statements",
        "Profit & Loss account and Balance Sheet for last 3 years"
      ]
    },
    {
      category: "Property Documents",
      documents: [
        "Sale deed/Agreement for sale",
        "Property registration documents",
        "Property tax receipts",
        "NOC from builder/society",
        "Approved building plan",
        "Previous loan documents (if any)"
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-green-600 mb-8">Mortgage Loan Services</h1>
      
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-green-700 mb-4">Why Choose Our Mortgage Services?</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-xl font-semibold text-green-600 mb-2">Competitive Interest Rates</h3>
            <p className="text-gray-700">Starting from 8.5% p.a. with flexible repayment options up to 30 years</p>
          </div>
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-xl font-semibold text-green-600 mb-2">Quick Processing</h3>
            <p className="text-gray-700">Fast approval process with minimal documentation and quick disbursement</p>
          </div>
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-xl font-semibold text-green-600 mb-2">Expert Guidance</h3>
            <p className="text-gray-700">Dedicated loan officers to guide you through the entire process</p>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-green-700 mb-6">Required Documents</h2>
        <div className="grid md:grid-cols-2 gap-6">
          {documentRequirements.map((category, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-green-600 mb-4">{category.category}</h3>
              <ul className="list-disc list-inside space-y-2">
                {category.documents.map((doc, docIndex) => (
                  <li key={docIndex} className="text-gray-700">{doc}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-green-50 p-6 rounded-lg">
        <h2 className="text-2xl font-semibold text-green-700 mb-4">Ready to Apply?</h2>
        <p className="text-gray-700 mb-4">Contact our mortgage specialists today to get started with your loan application.</p>
        <div className="flex gap-4">
          <a href="tel:+919998860713" className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition-colors">
            Call Now
          </a>
          <a href="https://wa.me/919998860713" className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition-colors">
            WhatsApp Us
          </a>
        </div>
      </div>
    </div>
  );
};

export default MortgageLoan; 