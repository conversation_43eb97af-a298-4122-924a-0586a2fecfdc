import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import ImageAssets from './ImageAssets'

const HomeSlider = () => {
  const slides = [
    {
      image: ImageAssets.banners.banner1,
      title: "Finding Your",
      highlight: "Dream Home",
      description: "Allow us to assist you in locating the plan for your ideal house.",
      primaryButton: {
        text: "Buy A Home",
        link: "/properties",
        icon: "🛒"
      },
      secondaryButton: {
        text: "Refinance",
        link: "/loans"
      },
      badge: {
        text: "Top Rated",
        subtext: "Mortgage Lender"
      }
    },
    {
      image: ImageAssets.banners.banner2,
      title: "Secure Your",
      highlight: "Dream Loan",
      description: "Get the best mortgage rates and financing options for your new home.",
      primaryButton: {
        text: "Apply Now",
        link: "/loans",
        icon: "💰"
      },
      secondaryButton: {
        text: "Learn More",
        link: "/about"
      },
      badge: {
        text: "Top Rated",
        subtext: "Mortgage Lender"
      }
    },
    {
      image: ImageAssets.banners.banner3,
      title: "Transform Your",
      highlight: "Living Space",
      description: "Professional interior design services to make your house feel like home.",
      primaryButton: {
        text: "Book Consultation",
        link: "/interiors",
        icon: "🏡"
      },
      secondaryButton: {
        text: "View Portfolio",
        link: "/interiors"
      },
      badge: {
        text: "Top Rated",
        subtext: "Design Service"
      }
    }
  ]

  const [currentSlide, setCurrentSlide] = useState(0)

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
    }, 5000) // Change slide every 5 seconds
    return () => clearInterval(interval)
  }, [slides.length])

  // Manual navigation
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1))
  }

  return (
    <section className="relative w-full overflow-visible">
      {/* Slider */}
      <div className="relative h-[600px] overflow-hidden rounded-2xl mx-4">
        {slides.map((slide, index) => (
          <div 
            key={index}
            className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
              index === currentSlide ? 'opacity-100 z-10 translate-x-0' : 
              index < currentSlide ? 'opacity-0 z-0 -translate-x-full' : 'opacity-0 z-0 translate-x-full'
            }`}
          >
            {/* Background Image */}
            <div className="absolute inset-0">
              <img 
                src={slide.image} 
                alt={`Slide ${index + 1}`} 
                className="w-full h-full object-cover rounded-2xl"
              />
              {/* Dark overlay for better text readability */}
              <div className="absolute inset-0 bg-black bg-opacity-20 rounded-2xl"></div>
            </div>
            
            {/* Content */}
            <div className="relative z-20 h-full flex items-center">
              <div className="container mx-auto px-8 md:px-16">
                <div className="max-w-xl">
                  <h1 className="text-white text-4xl md:text-5xl lg:text-6xl font-bold mb-2">
                    {slide.title}
                  </h1>
                  <h1 className="text-green-400 text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                    {slide.highlight}
                  </h1>
                  <p className="text-white text-lg md:text-xl mb-8">
                    {slide.description}
                  </p>
                  
                  <div className="flex items-center gap-4">
                    <Link 
                      to={slide.primaryButton.link} 
                      className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                    >
                      {slide.primaryButton.text}
                      <span>{slide.primaryButton.icon}</span>
                    </Link>
                    
                    <Link 
                      to={slide.secondaryButton.link} 
                      className="text-white hover:text-green-400 transition-colors flex items-center gap-2"
                    >
                      {slide.secondaryButton.text}
                      <span className="text-white">→</span>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Navigation Arrows - positioned on right side */}
        <div className="absolute right-8 top-1/2 -translate-y-1/2 z-30 flex flex-col gap-2">
          <button 
            onClick={prevSlide}
            className="bg-white rounded-full w-10 h-10 flex items-center justify-center text-green-700 hover:bg-green-50 transition-colors"
            aria-label="Previous slide"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button 
            onClick={nextSlide}
            className="bg-white rounded-full w-10 h-10 flex items-center justify-center text-green-700 hover:bg-green-50 transition-colors"
            aria-label="Next slide"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Badge - positioned to overlap the bottom of the slider */}
      <div className="absolute right-12 bottom-0 translate-y-1/2 bg-white bg-opacity-90 rounded-xl p-4 flex items-center gap-4 max-w-xs shadow-md z-50">
        <div className="bg-green-700 rounded-full w-16 h-16 flex items-center justify-center text-white">
          <span className="font-bold text-sm">ISO</span>
        </div>
        <div>
          <div className="font-bold text-gray-900">{slides[currentSlide].badge.text}</div>
          <div className="text-gray-700">{slides[currentSlide].badge.subtext}</div>
        </div>
      </div>
    </section>
  )
}

export default HomeSlider
