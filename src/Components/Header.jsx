import React from 'react'
import { Link } from 'react-router-dom'
import Logo from "../assets/logo.png";

const Header = () => {
  return (
    <>
      <div className="bg-green-50 text-green-800 py-2">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm">
            <div className="flex flex-wrap justify-center md:justify-start gap-2 md:gap-4 mb-2 md:mb-0 text-center md:text-left">
              <a href="https://wa.me/919998860713" className="flex items-center hover:text-green-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-whatsapp mr-1" viewBox="0 0 16 16">
                  <path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592z"/>
                </svg>
                +91 99988 60713 
              </a>
              <span className="hidden md:inline">|</span>
              <span>Mon - Sat 9:00 - 19:00 </span>
              <span className="hidden md:inline">|</span>
              <span className="hidden lg:inline">Dev Angan Building, C-5, Devchand Nagar, Bhayandar West</span>
            </div>
            <div className="flex gap-4">
              <Link to="/about" className="hover:text-green-600 transition-colors">About Us</Link>
              <Link to="/contact" className="hover:text-green-600 transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </div>
      
      <header className="bg-white text-green-600 shadow-md">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Link to="/">
                <img src={Logo} alt="One-Stop Home Solutions" className="w-14 mr-3" />
              </Link>
              <h1 className="text-2xl font-bold text-green-600">Aashirya</h1>
            </div>
            
            <nav className="flex flex-wrap justify-center gap-4">
              <Link to="/" className="hover:text-green-800 transition-colors">Home</Link>
              <Link to="/properties" className="hover:text-green-800 transition-colors">Properties</Link>
              <Link to="/loans" className="hover:text-green-800 transition-colors">Loans</Link>
              <Link to="/interiors" className="hover:text-green-800 transition-colors">Interiors</Link>
              <Link to="/services" className="hover:text-green-800 transition-colors">Services</Link>
            </nav>
            
            <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
              <Link to="/properties" className="border-2 border-green-600 text-green-600 px-3 py-1 rounded hover:bg-green-50 transition-colors text-sm">
                Find Your Property
              </Link>
              <Link to="/loans" className="border-2 border-green-600 text-green-600 px-3 py-1 rounded hover:bg-green-50 transition-colors text-sm">
                Apply for a Loan
              </Link>
            </div>
          </div>
        </div>
      </header>
    </>
  )
}

export default Header
