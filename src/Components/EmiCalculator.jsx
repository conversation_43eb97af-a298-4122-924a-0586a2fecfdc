import React, { useState, useEffect } from 'react';

const EmiCalculator = () => {
  const [loanAmount, setLoanAmount] = useState(3000000);  // 30 lakhs
  const [interestRate, setInterestRate] = useState(8.5);  // Typical Indian home loan rate
  const [loanTerm, setLoanTerm] = useState(20);          // 20 years is common in India
  const [monthlyPayment, setMonthlyPayment] = useState(0);
  const [totalPayment, setTotalPayment] = useState(0);
  const [totalInterest, setTotalInterest] = useState(0);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (showResults) {
      calculateMonthlyPayment();
    }
  }, [loanAmount, interestRate, loanTerm, showResults]);

  const calculateMonthlyPayment = () => {
    // Convert interest from annual percentage to monthly decimal
    const monthlyRate = interestRate / 100 / 12;
    
    // Convert years to months
    const numberOfPayments = loanTerm * 12;
    
    // Calculate the monthly payment
    const principal = loanAmount;
    
    // Use the EMI formula: EMI = P × r × (1 + r)^n / ((1 + r)^n - 1)
    const payment = principal * monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments) / 
                   (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    
    // Calculate total payment and interest
    const totalPaid = payment * numberOfPayments;
    const totalInterestPaid = totalPaid - principal;
    
    setMonthlyPayment(payment);
    setTotalPayment(totalPaid);
    setTotalInterest(totalInterestPaid);
    setShowResults(true);
  };

  // Format number to Indian currency format (with lakhs and crores)
  const formatIndianCurrency = (num) => {
    const value = Math.round(num);
    // Convert to string and add commas for Indian number format
    const result = value.toString().replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");
    return result;
  };

  return (
    <div className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-12">
          EMI Calculator
        </h2>
        
        <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="flex flex-col md:flex-row">
            {/* Calculator Form */}
            <div className="w-full md:w-1/2 bg-green-50 p-8">
              <h3 className="text-xl font-semibold text-green-700 mb-6">Calculate Your EMI</h3>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Loan Amount (₹)</label>
                <input
                  type="number"
                  value={loanAmount}
                  onChange={(e) => setLoanAmount(Number(e.target.value))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="3000000"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Interest Rate (% per annum)</label>
                <input
                  type="number"
                  value={interestRate}
                  onChange={(e) => setInterestRate(Number(e.target.value))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="8.5"
                  step="0.1"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Loan Term (Years)</label>
                <input
                  type="number"
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(Number(e.target.value))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="20"
                />
              </div>
              
              <button 
                onClick={calculateMonthlyPayment}
                className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                Calculate EMI
              </button>
            </div>
            
            {/* Results */}
            <div className="w-full md:w-1/2 p-8">
              <h3 className="text-xl font-semibold text-green-700 mb-6">Loan Summary</h3>
              
              {showResults ? (
                <div className="space-y-6">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 mb-1">Monthly EMI</p>
                    <p className="text-2xl font-bold text-green-700">₹{formatIndianCurrency(monthlyPayment)}</p>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 mb-1">Total Amount Payable</p>
                    <p className="text-2xl font-bold text-gray-700">₹{formatIndianCurrency(totalPayment)}</p>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 mb-1">Total Interest Payable</p>
                    <p className="text-2xl font-bold text-gray-700">₹{formatIndianCurrency(totalInterest)}</p>
                  </div>
                  
                  <div className="mt-6 text-sm text-gray-500">
                    <p>* This is an approximate calculation based on the information provided.</p>
                    <p>* Actual EMI may vary based on the lender's terms and conditions.</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500 text-center">Fill in the details and click "Calculate EMI" to see your loan summary.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmiCalculator;