import React from 'react'
import { Link } from 'react-router-dom'
import ImageAssets from './ImageAssets'

const VisionMission = () => {
  const achievements = [
    { number: "500+", label: "Happy Clients" },
    { number: "150+", label: "Projects Completed" },
    { number: "50+", label: "Expert Team Members" },
    { number: "10+", label: "Years of Experience" }
  ];

  const milestones = [
    {
      year: "2013",
      title: "Company Founded",
      description: "Started with a small team of 5 passionate individuals focused on property services."
    },
    {
      year: "2015",
      title: "Expanded to Financing",
      description: "Added mortgage and loan services to provide comprehensive solutions."
    },
    {
      year: "2018",
      title: "Interior Design Division",
      description: "Launched our interior design division to help clients transform their spaces."
    },
    {
      year: "2021",
      title: "Digital Transformation",
      description: "Embraced technology with our integrated platform for seamless service delivery."
    }
  ];

  return (
    <>
      {/* Hero Banner */}
      <section className="relative py-20 bg-green-700 overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <img 
            src="https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80" 
            alt="Background" 
            className="w-full h-full object-cover"
          />
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Transforming Home Experiences</h2>
            <p className="text-xl text-green-100 mb-10">
              Our integrated approach combines property, finance, design, and maintenance 
              to create seamless experiences for homeowners across India.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/about" className="bg-white text-green-700 px-8 py-4 rounded-lg hover:bg-green-50 transition-colors text-lg font-medium">
                Our Story
              </Link>
              <Link to="/contact" className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors text-lg font-medium">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Vision & Mission Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Vision & Mission</h2>
            <div className="w-24 h-1 bg-green-600 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Guided by our core values, we're committed to transforming how people experience home services.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Vision */}
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="flex items-center mb-6">
                <div className="bg-green-100 p-3 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-green-700">Our Vision</h3>
              </div>
              
              <p className="text-gray-700 mb-6 text-lg">
                To become India's most trusted integrated platform for property, finance, design, and home care, 
                revolutionizing how people find, finance, design, and maintain their homes.
              </p>
              
              <div className="bg-green-50 p-6 rounded-lg border-l-4 border-green-600">
                <p className="text-gray-700 italic">
                  "We envision a future where every homeowner and tenant has access to seamless, 
                  transparent, and high-quality home services through a single trusted platform."
                </p>
              </div>
            </div>
            
            {/* Mission */}
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="flex items-center mb-6">
                <div className="bg-green-100 p-3 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-green-700">Our Mission</h3>
              </div>
              
              <p className="text-gray-700 mb-6 text-lg">
                To simplify the home journey by providing integrated solutions for property search, 
                financing, interior design, and maintenance through a customer-centric approach.
              </p>
              
              <div className="bg-green-50 p-6 rounded-lg border-l-4 border-green-600">
                <p className="text-gray-700 italic">
                  "We are committed to eliminating the hassles of home management by connecting customers 
                  with verified professionals, transparent pricing, and exceptional service quality."
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-10">
            {achievements.map((item, index) => (
              <div key={index} className="text-center">
                <h3 className="text-4xl md:text-5xl font-bold text-green-600 mb-2">{item.number}</h3>
                <p className="text-gray-700 text-lg">{item.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Left Image */}
            <div className="w-full lg:w-1/2">
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                  alt="Team meeting" 
                  className="rounded-xl shadow-xl w-full h-[500px] object-cover"
                />
                <div className="absolute -bottom-6 -right-6 bg-green-600 text-white p-6 rounded-lg shadow-lg max-w-xs">
                  <p className="font-medium text-lg">
                    "We believe in creating lasting relationships with our clients through trust and exceptional service."
                  </p>
                </div>
              </div>
            </div>
            
            {/* Right Content */}
            <div className="w-full lg:w-1/2">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Unique Approach</h2>
              <div className="w-20 h-1 bg-green-600 mb-8"></div>
              
              <div className="space-y-6">
                <div className="flex">
                  <div className="bg-green-100 p-3 rounded-full h-12 w-12 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-green-600 font-bold">01</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Integrated Solutions</h3>
                    <p className="text-gray-600">
                      We offer end-to-end services across property, finance, design, and maintenance, 
                      eliminating the need for multiple service providers.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-green-100 p-3 rounded-full h-12 w-12 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-green-600 font-bold">02</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Technology-Driven</h3>
                    <p className="text-gray-600">
                      Our digital platform connects clients with services seamlessly, providing 
                      transparency and convenience throughout the process.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-green-100 p-3 rounded-full h-12 w-12 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-green-600 font-bold">03</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Quality Assurance</h3>
                    <p className="text-gray-600">
                      We rigorously vet all service providers and partners to ensure our clients 
                      receive only the highest quality services.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-green-100 p-3 rounded-full h-12 w-12 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-green-600 font-bold">04</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Customer-First Mindset</h3>
                    <p className="text-gray-600">
                      Every decision we make is guided by what's best for our customers, ensuring 
                      their needs are always our top priority.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Milestones */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Journey</h2>
            <div className="w-24 h-1 bg-green-600 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              From humble beginnings to becoming a leading home services provider, our journey has been defined by growth and innovation.
            </p>
          </div>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-green-200"></div>
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex flex-col md:flex-row ${index % 2 === 0 ? 'md:flex-row-reverse' : ''}`}>
                  <div className="md:w-1/2"></div>
                  <div className="hidden md:flex justify-center items-center">
                    <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center z-10">
                      <span className="text-white font-bold">{milestone.year}</span>
                    </div>
                  </div>
                  <div className="md:w-1/2 bg-gray-50 p-6 rounded-xl shadow-md md:mr-6 md:ml-6">
                    <div className="md:hidden mb-2">
                      <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-bold">{milestone.year}</span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{milestone.title}</h3>
                    <p className="text-gray-600">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <div className="w-24 h-1 bg-green-600 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              These principles guide everything we do and define who we are as an organization.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-md text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-800 mb-2">Trust & Integrity</h4>
              <p className="text-gray-600">
                We build lasting relationships based on honesty, transparency, and ethical business practices.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-800 mb-2">Innovation</h4>
              <p className="text-gray-600">
                We continuously improve our services and embrace technology to enhance customer experience.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-800 mb-2">Customer-Centric</h4>
              <p className="text-gray-600">
                We put our customers first, understanding their needs and exceeding their expectations.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-800 mb-2">Quality Excellence</h4>
              <p className="text-gray-600">
                We maintain the highest standards in every service we provide, ensuring lasting satisfaction.
              </p>
            </div>
          </div>
        </div>
      </section>
        
      {/* CTA */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Link to="/about" className="inline-block bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors text-lg font-medium">
              Learn More About Us
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}

export default VisionMission
