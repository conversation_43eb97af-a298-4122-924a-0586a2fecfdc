import React from 'react'
import { Link } from 'react-router-dom'

const MortgageTools = () => {
  const services = [
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>,
      title: "Find Properties",
      description: "Discover your dream home from our extensive property listings with advanced search filters for location, price, and amenities. Our curated selection includes houses, apartments, and commercial spaces.",
      linkText: "Learn More",
      linkTo: "/properties"
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>,
      title: "Home Loans",
      description: "Get the best mortgage rates and financing options for your new home. Our loan specialists help you navigate through various loan types including fixed-rate, adjustable-rate, and government-backed options.",
      linkText: "Learn More",
      linkTo: "/loans"
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
            </svg>,
      title: "Interior Design",
      description: "Transform your space with our professional interior design services. Our expert designers create personalized concepts that reflect your style, optimize functionality, and enhance the aesthetic appeal of your home.",
      linkText: "Learn More",
      linkTo: "/interiors"
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>,
      title: "Home Services",
      description: "Access a wide range of essential home maintenance and repair services. From plumbing and electrical work to cleaning and landscaping, our vetted professionals deliver quality service for all your home care needs.",
      linkText: "Learn More",
      linkTo: "/services"
    }
  ]

  return (
    <section className="py-16 bg-gray-50 w-full overflow-x-hidden">
      <div className="container mx-auto px-4 max-w-full">
        {/* Heading */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            You know the home you want. We make
            <br className="hidden md:block" />
            it happen for your need.
          </h2>
        </div>
        
        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => (
            <div key={index} className="flex flex-col items-center group bg-white p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 hover:bg-green-50 h-full">
              {/* Icon Circle */}
              <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mb-6 transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg">
                {service.icon}
              </div>
              
              {/* Title */}
              <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center transition-colors duration-300 group-hover:text-green-600">
                {service.title}
              </h3>
              
              {/* Description */}
              <p className="text-gray-600 mb-4 text-center group-hover:text-gray-700">
                {service.description}
              </p>
              
              {/* Link */}
              <Link 
                to={service.linkTo} 
                className="mt-auto flex items-center text-green-600 font-medium hover:text-green-800 transition-colors group-hover:underline"
              >
                {service.linkText}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 transition-transform duration-300 group-hover:translate-x-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default MortgageTools
