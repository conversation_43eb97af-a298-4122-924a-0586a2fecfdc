import React, { useState, useEffect } from 'react'
import ImageAssets from './ImageAssets'

const MortgageCalculator = () => {
  const [loanAmount, setLoanAmount] = useState(3000000);  // 30 lakhs
  const [interestRate, setInterestRate] = useState(8.5);  // Typical Indian home loan rate
  const [downPayment, setDownPayment] = useState(600000); // 6 lakhs
  const [loanTerm, setLoanTerm] = useState(20);          // 20 years is common in India
  const [monthlyPayment, setMonthlyPayment] = useState(0);
  const [totalPayment, setTotalPayment] = useState(0);
  const [totalInterest, setTotalInterest] = useState(0);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (showResults) {
      calculateMonthlyPayment();
    }
  }, [loanAmount, interestRate, downPayment, loanTerm, showResults]);

  const calculateMonthlyPayment = () => {
    // Calculate the loan amount after down payment
    const principal = loanAmount - downPayment;
    
    // Convert annual interest rate to monthly and decimal form
    const monthlyInterest = interestRate / 100 / 12;
    
    // Convert loan term from years to months
    const numberOfPayments = loanTerm * 12;
    
    // Calculate monthly payment using the mortgage formula
    let payment;
    if (monthlyInterest === 0) {
      payment = principal / numberOfPayments;
    } else {
      payment = principal * 
        (monthlyInterest * Math.pow(1 + monthlyInterest, numberOfPayments)) / 
        (Math.pow(1 + monthlyInterest, numberOfPayments) - 1);
    }
    
    // Calculate total payment and interest
    const totalPaid = payment * numberOfPayments;
    const totalInterestPaid = totalPaid - principal;
    
    setMonthlyPayment(payment);
    setTotalPayment(totalPaid);
    setTotalInterest(totalInterestPaid);
    setShowResults(true);
  };

  // Format number to Indian currency format (with lakhs and crores)
  const formatIndianCurrency = (num) => {
    const value = Math.round(num);
    // Convert to string and add commas for Indian number format
    const result = value.toString().replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");
    return result;
  };

  return (
    <>
      <section className="py-20 bg-[#0a1500] text-white relative m-4 rounded-xl">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-start">
            {/* Calculator Form */}
            <div className="w-full lg:w-1/2 bg-[#f8fff0] text-gray-800 p-6 sm:p-8 rounded-xl shadow-lg">
              <h2 className="text-xl sm:text-2xl font-bold mb-6">Calculate your Home Loan</h2>
              
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Property Value</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <span className="text-gray-500">₹</span>
                  </div>
                  <input
                    type="text"
                    value={formatIndianCurrency(loanAmount)}
                    onChange={(e) => setLoanAmount(Number(e.target.value.replace(/,/g, '')))}
                    className="w-full p-3 pl-8 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="30,00,000"
                  />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Interest Rate</label>
                <div className="relative">
                  <input
                    type="text"
                    value={interestRate}
                    onChange={(e) => setInterestRate(Number(e.target.value))}
                    className="w-full p-3 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="8.5"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <span className="text-gray-500">%</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Down Payment</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <span className="text-gray-500">₹</span>
                  </div>
                  <input
                    type="text"
                    value={formatIndianCurrency(downPayment)}
                    onChange={(e) => setDownPayment(Number(e.target.value.replace(/,/g, '')))}
                    className="w-full p-3 pl-8 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="6,00,000"
                  />
                </div>
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">Loan Term (Years)</label>
                <input
                  type="text"
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(Number(e.target.value))}
                  className="w-full p-3 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                  placeholder="20"
                />
              </div>
              
              <button 
                onClick={calculateMonthlyPayment}
                className="w-full bg-green-700 text-white py-3 rounded-md font-medium hover:bg-green-800 transition-colors mb-6"
              >
                Calculate EMI
              </button>
              
              {showResults && (
                <div className="p-4 bg-green-50 rounded-lg border border-green-100">
                  <h3 className="font-semibold text-lg text-green-800 mb-3">Loan Summary</h3>
                  
                  <div className="space-y-2 mb-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Principal Amount:</span>
                      <span className="font-medium">₹{formatIndianCurrency(loanAmount - downPayment)}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Years:</span>
                      <span className="font-medium">{loanTerm}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Monthly EMI:</span>
                      <span className="font-medium">₹{formatIndianCurrency(monthlyPayment)}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Balance Payable With Interest:</span>
                      <span className="font-medium">₹{formatIndianCurrency(totalPayment)}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total With Down Payment:</span>
                      <span className="font-medium">₹{formatIndianCurrency(totalPayment + downPayment)}</span>
                    </div>
                  </div>
                  
                  <div className="pt-3 border-t border-green-200">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Total Interest Paid:</span>
                      <span className="font-bold text-green-700">₹{formatIndianCurrency(totalInterest)}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Text Content */}
            <div className="w-full lg:w-1/2 lg:pl-12 mt-8 lg:mt-0 relative">
              <div className="mb-32 sm:mb-64">
                <h2 className="text-2xl sm:text-3xl md:text-5xl font-bold mb-6 leading-tight">
                  We help All people to find their best and dream house from Laks
                </h2>
                
                <p className="text-gray-300 mb-8 text-base sm:text-lg">
                  Most people find that home loan financing is complicated and confusing. 
                  We help you buy your dream home by simplifying the home loan process 
                  with personalized options that save you time and money.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-300">Competitive interest rates</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-300">Fast approval process</span>
                  </div>
                </div>
              </div>
              
              {/* Image container - normal on mobile, absolute on larger screens */}
              <div className="relative sm:absolute sm:bottom-0 sm:top-24 sm:left-14 sm:right-0 sm:translate-y-1/2 rounded-xl overflow-hidden shadow-xl mt-8 sm:mt-0">
                <img 
                  src={ImageAssets.content.calculate} 
                  alt="Mortgage calculation" 
                  className="w-full h-[400px] sm:h-[500px] object-cover object-center"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Stats Section - positioned to account for the overflowing image */}
      <section className="pt-6 pb-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-start items-center md:space-x-24">
            <div className="text-center mb-8 md:mb-0">
              <h3 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-2">78%</h3>
              <p className="text-gray-600">Business from Referrals</p>
            </div>
            
            <div className="text-center">
              <h3 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-2">45+</h3>
              <p className="text-gray-600">Minute Verbal Commitment</p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default MortgageCalculator
