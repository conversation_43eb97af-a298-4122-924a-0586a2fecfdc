import React from 'react';

const LoadingScreen = ({ isVisible }) => {
  if (!isVisible) return null;
  
  return (
    <div className="fixed inset-0 bg-white bg-opacity-80 z-50 flex items-center justify-center transition-opacity duration-300">
      <div className="loader">
        <div className="spinner"></div>
        <p className="mt-4 text-green-700 font-medium">Loading...</p>
      </div>
    </div>
  );
};

export default LoadingScreen;