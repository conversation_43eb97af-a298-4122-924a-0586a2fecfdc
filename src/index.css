@tailwind base;
@tailwind components;
@tailwind utilities;

/* House shape styling */
.house-shape-container {
  position: relative;
  width: 100%;
  padding-bottom: 90%; /* Adjust aspect ratio to match house shape */
}

.house-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* House silhouette shape */
  clip-path: polygon(
    0% 50%,    /* Left point where roof meets base */
    50% 0%,    /* Top point of roof */
    100% 50%,  /* Right point where roof meets base */
    100% 100%, /* Bottom right corner */
    0% 100%    /* Bottom left corner */
  );
  overflow: hidden;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Background decorative elements */
.bg-pattern {
  position: absolute;
  z-index: -1;
  opacity: 0.5;
}

