import { useState } from 'react';

/**
 * Custom hook for managing loading states
 * @param {boolean} initialState - Initial loading state
 * @returns {Array} - Array containing loading state and functions to control it
 */
const useLoading = (initialState = false) => {
  const [isLoading, setIsLoading] = useState(initialState);
  
  /**
   * Wraps an async function with loading state management
   * @param {Function} asyncFunction - The async function to wrap
   * @returns {Function} - Wrapped function that manages loading state
   */
  const withLoading = async (asyncFunction) => {
    try {
      setIsLoading(true);
      const result = await asyncFunction();
      return result;
    } finally {
      setIsLoading(false);
    }
  };
  
  return [isLoading, setIsLoading, withLoading];
};

export default useLoading;