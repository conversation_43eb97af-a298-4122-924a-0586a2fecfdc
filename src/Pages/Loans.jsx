import React, { useState, useRef } from 'react'
import { Link, useNavigate } from 'react-router-dom';
import { sendFormData } from '../utils/emailService';

const Loans = () => {
  const navigate = useNavigate();
  const documentsRef = useRef(null);
  const [formData, setFormData] = useState({
    name: '',
    contact: '',
    loanType: '',
    amount: '',
    income: '',
  });

  // Scroll to Documents Assistance section
  const scrollToDocuments = () => {
    documentsRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Loan application submitted:', formData);
    
    // Send email
    sendFormData('template_loans', {
      form_name: 'Loan Application',
      name: formData.name,
      contact: formData.contact,
      loan_type: formData.loanType,
      amount: formData.amount,
      income: formData.income
    })
      .then((response) => {
        console.log('Email sent successfully!', response);
        // Reset form after submission
        setFormData({
          name: '',
          contact: '',
          loanType: '',
          amount: '',
          income: '',
        });
        // Show success message
        alert('Thank you for your application. Our loan expert will contact you shortly!');
      })
      .catch((error) => {
        console.error('Failed to send email:', error);
        alert('There was an error submitting your form. Please try again later.');
      });
  };

  const loanTypes = [
    {
      title: "Mortgage Loans",
      icon: "🏦",
      description: "Get competitive mortgage loans with flexible terms and quick processing for your property purchase or refinancing needs.",
      features: [
        "Enjoy low starting rates on your mortgage.",
        
        "Tenure up to 30 years",
        "Quick processing and approval",
        "Dedicated mortgage specialists"
      ],
      path: "/mortgage"
    },
    {
      title: "Home Loans",
      icon: "🏠",
      description: "Finance your dream home with loans up to ₹10 Cr from top banks at the lowest interest rates in the market.",
      features: [
        
        "Your dream home starts with the best loan rates.",
        "Flexible repayment options up to 30 years",
        "Special rates for women applicants",
        "Quick approval process"
      ],
      path: "/home-loan"
    },
    {
      title: "Personal Loans",
      icon: "💼",
      description: "Get quick access to funds for your personal needs with minimal documentation and fast approval.",
      features: [
        
        "Quick disbursal within 48 hours",
        "Minimal documentation required",
        "No collateral needed",
        "Flexible end-use"
      ],
      path: "/personal-loan"
    },
    {
      title: "Education Loans",
      icon: "🎓",
      description: "Invest in your future with education loans for studies in India or abroad with special interest rates.",
      features: [
        "Cover tuition fees, living expenses, and equipment",
        "Special interest rates for premier institutions",
        "Moratorium period during course duration",
        "Tax benefits under Section 80E",
        "100% financing for studies abroad"
      ],
      path: "/education-loan"
    }
  ];

  const documents = {
    homeMortgage: [
      {
        category: "Identity & Address Proof",
        items: [
          "PAN Card",
          "Aadhaar Card",
          "Passport/Voter's ID/Driving License",
          "Latest Utility Bills (not older than 3 months)",
          "Passport size photographs"
        ]
      },
      {
        category: "Income Documents (Salaried)",
        items: [
          "Last 3 months salary slips",
          "Form 16 for the last 2 years",
          "Last 6 months bank statements",
          "Employment certificate & ID card",
          "Income Tax Returns for last 2 years"
        ]
      },
      {
        category: "Income Documents (Self-Employed)",
        items: [
          "Income Tax Returns for last 3 years",
          "Business registration proof",
          "GST returns (if applicable)",
          "Last 12 months bank statements",
          "Profit & Loss account and Balance Sheet"
        ]
      },
      {
        category: "Property Documents",
        items: [
          "Sale deed/Agreement for sale",
          "Property registration documents",
          "Property tax receipts",
          "NOC from builder/society",
          "Approved building plan",
          "Previous loan documents (if any)"
        ]
      }
    ],
    education: [
      {
        category: "Basic Documents",
        items: [
          "PAN Card",
          "Aadhaar Card",
          "Passport size photographs",
          "Birth Certificate",
          "Address proof"
        ]
      },
      {
        category: "Academic Documents",
        items: [
          "Mark sheets of last qualifying examination",
          "Admission letter from Institution",
          "Fee structure document",
          "Scholarship letter (if any)",
          "Previous academic certificates"
        ]
      },
      {
        category: "Financial Documents",
        items: [
          "Income proof of parents/guardian",
          "Last 6 months bank statements",
          "Form 16 or Income Tax Returns",
          "Collateral documents (if required)",
          "Co-borrower documents (if applicable)"
        ]
      },
      {
        category: "Additional Documents (For Study Abroad)",
        items: [
          "Valid Passport",
          "Visa or visa application documents",
          "I20 or admission confirmation",
          "IELTS/TOEFL/GRE/GMAT scores",
          "Cost estimation letter from institution"
        ]
      }
    ]
  };

  const banks = [
    "State Bank of India",
    "HDFC Bank",
    "ICICI Bank",
    "Axis Bank",
    "Bank of Baroda",
    "Punjab National Bank",
    "Kotak Mahindra Bank",
    "Yes Bank"
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-green-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl font-bold text-green-700 mb-4">
              Get Loans That Work for You – Home, Personal & Education
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-8">
              Compare, apply, and secure loans with ease. We handle the paperwork while you focus on your goals.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <button 
                onClick={scrollToDocuments}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                Check Eligibility
              </button>
              <button 
                onClick={() => navigate('/emi-calculator')}
                className="border-2 border-green-600 text-green-600 px-6 py-3 rounded-lg hover:bg-green-50 transition-colors"
              >
                Calculate EMI
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Loan Types Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center text-gray-800 mb-12">
            Loan Options Tailored to Your Needs
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {loanTypes.map((loan, index) => (
              <div key={index} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all p-6 border border-gray-100">
                <div className="text-4xl mb-4">{loan.icon}</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">{loan.title}</h3>
                <p className="text-gray-600 mb-6">{loan.description}</p>
                <div className="space-y-2 mb-6">
                  {loan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-start">
                      <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
                <button 
                  onClick={() => navigate(loan.path)}
                  className="w-full bg-green-50 text-green-600 font-medium py-2 rounded-lg hover:bg-green-100 transition-colors"
                >
                  Learn More
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Documents & Assistance Section */}
      <section ref={documentsRef} className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
            Documents Assistance
          </h2>
          <p className="text-gray-600 mb-8">
            We simplify the loan application process by helping you prepare and organize all necessary documents. Our experts will guide you through each step to ensure a smooth approval process.
          </p>

          {/* Home & Mortgage Loan Documents */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-green-700 mb-6">Home & Mortgage Loan Documents</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6">
              {documents.homeMortgage.map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-xl shadow-md">
                  <h4 className="text-lg font-semibold text-green-600 mb-4">{category.category}</h4>
                  <ul className="space-y-3">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Education Loan Documents */}
          <div>
            <h3 className="text-xl font-semibold text-green-700 mb-6">Education Loan Documents</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6">
              {documents.education.map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-xl shadow-md">
                  <h4 className="text-lg font-semibold text-green-600 mb-4">{category.category}</h4>
                  <ul className="space-y-3">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

        </div>
      </section>

      {/* Application Form Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="flex flex-col md:flex-row">
              {/* Left Content */}
              <div className="w-full md:w-2/5 bg-green-700 p-8 text-white">
                <h2 className="text-2xl font-bold mb-6">Apply for a Free Loan Consultation</h2>
                <p className="mb-8">
                  Fill out this simple form and our loan experts will get in touch with you to discuss your options and guide you through the application process.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Quick response within 24 hours</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span>100% secure & confidential</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    <span>No hidden charges</span>
                  </div>
                </div>
              </div>
              
              {/* Right Form */}
              <div className="w-full md:w-3/5 p-8">
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
                    <input
                      type="tel"
                      id="contact"
                      name="contact"
                      value={formData.contact}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Your phone number"
                      required
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="loanType" className="block text-sm font-medium text-gray-700 mb-1">Loan Type</label>
                    <select
                      id="loanType"
                      name="loanType"
                      value={formData.loanType}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      required
                    >
                      <option value="">Select Loan Type</option>
                      <option value="mortgage">Mortgage Loan</option>
                      <option value="home">Home Loan</option>
                      <option value="personal">Personal Loan</option>
                      <option value="education">Education Loan</option>
                    </select>
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">Amount Needed (₹)</label>
                    <input
                      type="text"
                      id="amount"
                      name="amount"
                      value={formData.amount}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Enter the amount you need"
                      required
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="income" className="block text-sm font-medium text-gray-700 mb-1">Monthly Income (₹)</label>
                    <input
                      type="text"
                      id="income"
                      name="income"
                      value={formData.income}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Enter your monthly income"
                      required
                    />
                  </div>
                  
                  {/* <div className="mb-4">
                    <label htmlFor="bank" className="block text-sm font-medium text-gray-700 mb-1">Preferred Bank</label>
                    <select
                      id="bank"
                      name="bank"
                      value={formData.bank}
                      onChange={handleChange}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      required
                    >
                      <option value="">Select a bank</option>
                      {banks.map((bank, index) => (
                        <option key={index} value={bank}>{bank}</option>
                      ))}
                    </select>
                  </div> */}
                  
                  <div className="mb-4">
                    <label htmlFor="terms" className="block text-sm font-medium text-gray-700 mb-1">Terms and Conditions</label>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="terms"
                        name="terms"
                        className="form-checkbox h-4 w-4 text-green-600 focus:ring-green-500"
                        required
                      />
                      <label htmlFor="terms" className="ml-2 text-sm text-gray-600">
                        I agree to the <Link to="/terms-and-condition" className="text-green-600 hover:underline">terms and conditions</Link>
                      </label>
                    </div>
                  </div>
                  
                  <button type="submit" className="w-full bg-green-600 text-white font-medium py-3 rounded-lg hover:bg-green-700 transition-colors">
                    Submit Application
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Loans
