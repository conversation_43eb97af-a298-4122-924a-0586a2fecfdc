import React, { useState } from 'react'

const TermsAndCondition = () => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const sections = [
    {
      title: "1. Loan Application and Processing",
      content: [
        "By submitting a loan application, you certify that all information provided is true and accurate.",
        "We reserve the right to verify all information provided in the loan application.",
        "Submission of an application does not guarantee loan approval.",
        "Processing fees, once paid, are non-refundable regardless of loan approval status.",
        "The final loan amount and interest rate will be determined based on our evaluation criteria."
      ]
    },
    {
      title: "2. Documentation Requirements",
      content: [
        "All required documents must be submitted in the format specified by us.",
        "Documents submitted become the property of One-Stop Home Solutions and will not be returned.",
        "Additional documents may be requested during the verification process.",
        "Incomplete documentation may result in delay or rejection of the loan application.",
        "All documents must be genuine and verifiable."
      ]
    },
    {
      title: "3. Interest Rates and Charges",
      content: [
        "Interest rates are subject to change without prior notice.",
        "Processing fees and other charges will be as per our current policies.",
        "Penal interest will be charged on delayed payments as per terms of the loan agreement.",
        "Foreclosure charges may be applicable as per the loan agreement.",
        "GST and other applicable taxes will be charged as per government regulations."
      ]
    },
    {
      title: "4. Loan Disbursement",
      content: [
        "Loan disbursement is subject to completion of all documentation and verification processes.",
        "The loan amount will be disbursed as per the agreed terms and conditions.",
        "We reserve the right to reject the loan application without providing any reason.",
        "Disbursement mode will be as per company policy and regulatory guidelines.",
        "Any changes in disbursement terms will be communicated in writing."
      ]
    },
    {
      title: "5. Repayment Terms",
      content: [
        "EMI payments must be made on or before the due date each month.",
        "Advance payment and part payment terms will be as per the loan agreement.",
        "Default in payment may affect your credit score and future borrowing capacity.",
        "Recovery proceedings may be initiated in case of payment defaults.",
        "Modification in repayment terms must be approved by us in writing."
      ]
    },
    {
      title: "6. Privacy and Data Protection",
      content: [
        "Your personal information will be protected as per our privacy policy.",
        "We may share your information with credit bureaus and regulatory authorities.",
        "Your contact information may be used for loan-related communications.",
        "We implement reasonable security practices to protect your data.",
        "You have the right to access and update your personal information."
      ]
    }
  ];

  return (
    <div className="bg-gray-50 min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Terms and Conditions
          </h1>
          <p className="text-gray-600 text-lg">
            Please read these terms and conditions carefully before applying for any loan products.
          </p>
        </div>

        {/* Video Section */}
        <div className="mb-12">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="aspect-w-16 aspect-h-9 relative">
              {isVideoPlaying ? (
                <iframe
                  className="w-full h-full absolute"
                  src="https://www.youtube.com/embed/YOUR_VIDEO_ID?autoplay=1"
                  title="Terms and Conditions Explanation"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              ) : (
                <div 
                  className="w-full h-full bg-gray-800 relative cursor-pointer group"
                  onClick={() => setIsVideoPlaying(true)}
                >
                  {/* Thumbnail Image */}
                  <img
                    src="https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3"
                    alt="Video Thumbnail"
                    className="w-full h-full object-cover opacity-80"
                  />
                  
                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center group-hover:bg-green-700 transition-colors">
                      <svg 
                        className="w-8 h-8 text-white ml-1" 
                        fill="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path d="M8 5v14l11-7z" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Video Info Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black to-transparent">
                    <h3 className="text-white text-xl font-semibold mb-2">
                      Understanding Our Terms & Conditions
                    </h3>
                    <p className="text-gray-200">
                      Watch this short video to learn about our loan terms and conditions in simple terms.
                    </p>
                  </div>
                </div>
              )}
            </div>
            <div className="p-6 border-t border-gray-100">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-green-500 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-800 mb-1">
                    Why Watch This Video?
                  </h4>
                  <p className="text-gray-600">
                    Get a clear understanding of our terms and conditions in just a few minutes. We've explained everything in simple language to help you make informed decisions about your loan application.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Last Updated */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-8 text-sm text-gray-600">
          Last Updated: {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
        </div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-semibold text-gray-800">
                  {section.title}
                </h2>
              </div>
              <div className="p-6">
                <ul className="space-y-4">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 flex items-center justify-center mt-0.5">
                        <div className="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                      </div>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Contact Section */}
        <div className="mt-12 bg-green-50 rounded-lg p-6 text-center">
          <h3 className="text-xl font-semibold text-green-700 mb-4">
            Have Questions About Our Terms?
          </h3>
          <p className="text-gray-600 mb-6">
            Our customer service team is here to help you understand our terms and conditions.
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="tel:+919998860713"
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              Contact Support
            </a>
          </div>
        </div>

        {/* Footer Note */}
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            These terms and conditions are subject to change. Please check regularly for updates.
            By using our services, you agree to be bound by these terms and conditions.
          </p>
        </div>
      </div>
    </div>
  )
}

export default TermsAndCondition