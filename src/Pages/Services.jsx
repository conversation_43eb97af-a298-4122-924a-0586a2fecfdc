import React from 'react';
import { Link } from 'react-router-dom';

const Services = () => {
  const serviceCategories = [
    {
      title: "Home Cleaning",
      description: "Professional cleaning services for your home",
      services: [
        { name: "Deep Cleaning", price: "₹3,999", duration: "6-8 hours" },
        { name: "Kitchen Cleaning", price: "₹1,499", duration: "2-3 hours" },
        { name: "Bathroom Cleaning", price: "₹1,299", duration: "1-2 hours" },
        { name: "Sofa Cleaning", price: "₹999", duration: "1-2 hours" }
      ]
    },
    {
      title: "Home Repairs",
      description: "Quick and reliable repair services",
      services: [
        { name: "Plumbing", price: "₹499", duration: "1 hour" },
        { name: "Electrical", price: "₹599", duration: "1 hour" },
        { name: "Carpentry", price: "₹799", duration: "1-2 hours" },
        { name: "Painting", price: "₹1,999", duration: "Per day" }
      ]
    },
    {
      title: "Home Maintenance",
      description: "Regular maintenance to keep your home in top condition",
      services: [
        { name: "AC Service", price: "₹899", duration: "1-2 hours" },
        { name: "Geyser Repair", price: "₹699", duration: "1 hour" },
        { name: "Water Purifier", price: "₹599", duration: "1 hour" },
        { name: "Pest Control", price: "₹1,499", duration: "2-3 hours" }
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-12 relative">
      {/* Coming Soon Overlay */}
      <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
        <div className="text-center p-8 bg-green-600 bg-opacity-90 rounded-xl shadow-2xl">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-4">Coming Soon</h2>
          <p className="text-xl text-green-100 mb-8">
            We're working hard to bring these services to you. Stay tuned!
          </p>
          <Link 
            to="/"
            className="inline-block bg-white text-green-700 px-8 py-3 rounded-lg font-medium hover:bg-green-50 transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-green-50 rounded-2xl p-8 mb-12">
        <h1 className="text-4xl font-bold text-green-700 mb-4">Home Services</h1>
        <p className="text-lg text-gray-700 mb-6">
          We offer a wide range of professional home services to keep your property in perfect condition.
          Book our trusted professionals for all your home maintenance and improvement needs.
        </p>
        <div className="flex flex-wrap gap-4">
          <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
            Book a Service
          </button>
          <button className="border-2 border-green-600 text-green-600 px-6 py-3 rounded-lg hover:bg-green-50 transition-colors">
            View All Services
          </button>
        </div>
      </div>

      {/* Service Categories */}
      {serviceCategories.map((category, index) => (
        <div key={index} className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">{category.title}</h2>
          <p className="text-gray-600 mb-6">{category.description}</p>
          
          {/* Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {category.services.map((service, serviceIndex) => (
              <div key={serviceIndex} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all p-6 border border-gray-100">
                <h3 className="text-xl font-semibold text-gray-800 mb-2">{service.name}</h3>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-green-600 font-bold text-lg">{service.price}</span>
                  <span className="text-gray-500 text-sm">{service.duration}</span>
                </div>
                <button className="w-full bg-green-50 text-green-600 font-medium py-2 rounded-lg hover:bg-green-100 transition-colors">
                  Book Now
                </button>
              </div>
            ))}
          </div>
        </div>
      ))}
      
      {/* Call to Action */}
      <div className="bg-green-700 text-white rounded-2xl p-8 flex flex-col md:flex-row justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold mb-2">Need a custom service?</h2>
          <p className="text-green-100">Contact us for personalized solutions tailored to your specific needs.</p>
        </div>
        <div className="mt-6 md:mt-0 flex flex-col sm:flex-row gap-3">
          <a href="/contact" className="bg-white text-green-700 px-6 py-3 rounded-lg font-medium hover:bg-green-50 transition-colors">
            Contact Us
          </a>
          <a href="https://wa.me/919998860713" target="_blank" rel="noopener noreferrer" className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-500 transition-colors flex items-center justify-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
            </svg>
            WhatsApp Us
          </a>
        </div>
      </div>
    </div>
  );
};

export default Services;
